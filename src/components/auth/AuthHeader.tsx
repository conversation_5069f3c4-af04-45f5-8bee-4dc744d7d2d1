"use client";

import { usePathname } from "next/navigation";
import Link from "next/link";
import Image from "next/image";

export function AuthHeader() {
  const pathname = usePathname();
  
  const getPageTitle = () => {
    if (pathname?.includes('/login')) return 'Đăng nhập';
    if (pathname?.includes('/register')) return 'Đăng ký';
    if (pathname?.includes('/forgot-password')) return 'Quên mật khẩu';
    if (pathname?.includes('/reset-password')) return 'Đặt lại mật khẩu';
    return 'Work Finder';
  };

  return (
    <div className="text-center mb-8">
      <Link href="/" className="inline-flex items-center space-x-3 group">
        <Image
          src="/logo.png"
          alt="Hirevo"
          width={48}
          height={48}
          className="w-12 h-12 transition-transform duration-300 group-hover:scale-105"
          priority
        />
        <div className="text-left">
          <div className="text-xl font-bold text-gray-900 group-hover:text-blue-600 transition-colors duration-300">
            Hirevo
          </div>
          <div className="text-sm text-gray-500 font-medium">
            {getPageTitle()}
          </div>
        </div>
      </Link>
    </div>
  );
}
