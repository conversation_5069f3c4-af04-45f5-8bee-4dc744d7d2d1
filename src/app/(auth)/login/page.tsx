import { Metadata } from "next";
import Link from "next/link";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { AUTH_ROUTES } from "@/constants/routes";

export const metadata: Metadata = {
  title: "Đăng nhập - Work Finder",
  description:
    "Đăng nhập vào tài khoản Work Finder để truy cập dashboard và ứng tuyển việc làm.",
};

export default function LoginPage() {
  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="text-center">
        <h1 className="text-2xl font-bold text-gray-900 mb-2">
          Chào mừng bạn quay lại
        </h1>
        <p className="text-gray-600">
          Mỗi ứng viên là một cơ hội - hôm nay bạn chọn trưởng lại cho doanh
          nghiệp! 🚀💼
        </p>
      </div>

      {/* Email/Password Form */}
      <form className="space-y-4">
        <div className="space-y-4">
          <div>
            <Label
              htmlFor="email"
              className="text-sm font-medium text-gray-700"
            >
              Tên đăng nhập
            </Label>
            <Input
              id="email"
              name="email"
              type="email"
              autoComplete="email"
              required
              placeholder="Nhập email hoặc tên đăng nhập"
              className="mt-1 h-12 border-gray-200 focus:border-blue-500 focus:ring-blue-500 transition-colors duration-200 rounded-lg"
            />
          </div>

          <div>
            <Label
              htmlFor="password"
              className="text-sm font-medium text-gray-700"
            >
              Mật khẩu
            </Label>
            <Input
              id="password"
              name="password"
              type="password"
              autoComplete="current-password"
              required
              placeholder="Nhập mật khẩu"
              className="mt-1 h-12 border-gray-200 focus:border-blue-500 focus:ring-blue-500 transition-colors duration-200 rounded-lg"
            />
          </div>
        </div>

        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <input
              id="remember-me"
              name="remember-me"
              type="checkbox"
              className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded transition-colors duration-200"
            />
            <label
              htmlFor="remember-me"
              className="ml-2 block text-sm text-gray-700"
            >
              Ghi nhớ đăng nhập
            </label>
          </div>

          <div className="text-sm">
            <Link
              href={AUTH_ROUTES.FORGOT_PASSWORD}
              className="font-medium text-blue-600 hover:text-blue-700 transition-colors duration-200"
            >
              Quên mật khẩu?
            </Link>
          </div>
        </div>

        <Button
          type="submit"
          className="w-full h-12 bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white font-medium rounded-lg transition-all duration-200 shadow-lg hover:shadow-xl"
        >
          Đăng nhập
        </Button>
      </form>

      {/* Sign up link */}
      <div className="text-center">
        <p className="text-sm text-gray-600">
          Chưa có tài khoản?{" "}
          <Link
            href={AUTH_ROUTES.REGISTER}
            className="font-medium text-blue-600 hover:text-blue-700 transition-colors duration-200"
          >
            Đăng ký miễn phí
          </Link>
        </p>
      </div>
    </div>
  );
}
